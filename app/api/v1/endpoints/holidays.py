"""
Holiday API Endpoints
FastAPI router for holiday management under /settings/holidays
"""

from datetime import date
from typing import Optional
from fastapi import APIRouter, Depends, status, Query
from fastapi.responses import JSONResponse
from app.schemas.holiday import (
    HolidayCreateRequest,
    HolidayUpdateRequest,
    HolidayResponse,
    HolidayListResponse,
)
from app.services.holiday_service import HolidayService
from app.core.factory import get_holiday_service
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.schemas.base_response import StandardResponse
from app.core.logging import logger
from app.core.responses.models import ErrorCodes


router = APIRouter()


@router.post(
    "/holidays",
    response_model=StandardResponse[HolidayResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create Holiday",
    description="Create a new holiday with validation for constraints and duplicate checking",
)
async def create_holiday(
    holiday_in: HolidayCreateRequest,
    service: HolidayService = Depends(get_holiday_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Create a new holiday."""
    try:
        holiday = await service.create_holiday(holiday_in)

        # If error response (dict or JSONResponse), return it directly
        if isinstance(holiday, (dict, JSONResponse)):
            return holiday

        # Log successful creation
        logger.info(
            f"Holiday created successfully: {holiday.date} ({holiday.holiday_type})",
            extra={
                "context": {
                    "holiday_id": str(holiday.id),
                    "user_id": str(getattr(current_user, 'id', 'unknown')),
                }
            },
        )

        # Convert to response schema
        holiday_dict = holiday.__dict__.copy()
        if "id" in holiday_dict and holiday_dict["id"] is not None:
            holiday_dict["id"] = str(holiday_dict["id"])

        return APIStandards.create_success_response(
            data=HolidayResponse(**holiday_dict),
            message="Holiday created successfully",
            title="Holiday Created",
        )

    except Exception as e:
        logger.error(f"Unexpected error creating holiday: {str(e)}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while creating the holiday",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.get(
    "/holidays",
    response_model=StandardResponse[HolidayListResponse],
    summary="List Holidays",
    description="Get a list of holidays with optional filtering and pagination",
)
async def list_holidays(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of records to return"
    ),
    search: Optional[str] = Query(
        None, description="Search term for holiday description"
    ),
    holiday_type: Optional[str] = Query(
        None, regex="^(PREDEFINED|PERSONAL)$", description="Filter by holiday type"
    ),
    start_date: Optional[date] = Query(
        None, description="Start date for date range filter (YYYY-MM-DD)"
    ),
    end_date: Optional[date] = Query(
        None, description="End date for date range filter (YYYY-MM-DD)"
    ),
    service: HolidayService = Depends(get_holiday_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Get a list of holidays with optional filtering."""
    try:
        # Validate date range
        if start_date and end_date and start_date > end_date:
            return APIStandards.create_error_response(
                error_message="Start date must be before or equal to end date",
                error_title="Invalid Date Range",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4003,
            )

        holidays = await service.list_holidays(
            skip=skip,
            limit=limit,
            search=search,
            holiday_type=holiday_type,
            start_date=start_date,
            end_date=end_date,
        )

        # If error response (JSONResponse), return it directly
        if isinstance(holidays, JSONResponse):
            return holidays

        # Get total count for pagination
        filters = {}
        if holiday_type:
            filters["holiday_type"] = holiday_type

        total_count = await service.count_holidays(filters)

        # Convert holidays to response format
        holiday_responses = []
        for holiday in holidays:
            holiday_dict = holiday.__dict__.copy()
            if "id" in holiday_dict and holiday_dict["id"] is not None:
                holiday_dict["id"] = str(holiday_dict["id"])
            holiday_responses.append(HolidayResponse(**holiday_dict))

        return APIStandards.create_success_response(
            data=HolidayListResponse(items=holiday_responses, total_count=total_count),
            message="Holidays retrieved successfully",
            title="Holidays List",
        )

    except Exception as e:
        logger.error(f"Unexpected error listing holidays: {str(e)}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while retrieving holidays",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.get(
    "/holidays/{holiday_id}",
    response_model=StandardResponse[HolidayResponse],
    summary="Get Holiday",
    description="Get a single holiday by its UUID",
)
async def get_holiday(
    holiday_id: str,
    service: HolidayService = Depends(get_holiday_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Get a single holiday by UUID."""
    try:
        holiday = await service.get_holiday(holiday_id)

        # If error response (JSONResponse), return it directly
        if isinstance(holiday, JSONResponse):
            return holiday

        # Convert to response schema
        holiday_dict = holiday.__dict__.copy()
        if "id" in holiday_dict and holiday_dict["id"] is not None:
            holiday_dict["id"] = str(holiday_dict["id"])

        return APIStandards.create_success_response(
            data=HolidayResponse(**holiday_dict),
            message="Holiday retrieved successfully",
            title="Holiday Details",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error retrieving holiday {holiday_id}: {str(e)}", exc_info=True
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while retrieving the holiday",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.put(
    "/holidays/{holiday_id}",
    response_model=StandardResponse[HolidayResponse],
    summary="Update Holiday",
    description="Update an existing holiday by its UUID",
)
async def update_holiday(
    holiday_id: str,
    holiday_in: HolidayUpdateRequest,
    service: HolidayService = Depends(get_holiday_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Update an existing holiday."""
    try:
        holiday = await service.update_holiday(holiday_id, holiday_in)

        # If error response (JSONResponse), return it directly
        if isinstance(holiday, JSONResponse):
            return holiday

        # Log successful update
        logger.info(
            f"Holiday updated successfully: {holiday_id}",
            extra={
                "context": {
                    "holiday_id": holiday_id,
                    "user_id": str(getattr(current_user, 'id', 'unknown')),
                }
            },
        )

        # Convert to response schema
        holiday_dict = holiday.__dict__.copy()
        if "id" in holiday_dict and holiday_dict["id"] is not None:
            holiday_dict["id"] = str(holiday_dict["id"])

        return APIStandards.create_success_response(
            data=HolidayResponse(**holiday_dict),
            message="Holiday updated successfully",
            title="Holiday Updated",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error updating holiday {holiday_id}: {str(e)}", exc_info=True
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while updating the holiday",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )


@router.delete(
    "/holidays/{holiday_id}",
    response_model=StandardResponse[dict],
    summary="Delete Holiday",
    description="Soft delete a holiday by its UUID",
)
async def delete_holiday(
    holiday_id: str,
    service: HolidayService = Depends(get_holiday_service),
    current_user: dict = Depends(get_current_user),
) -> JSONResponse:
    """Soft delete a holiday."""
    try:
        result = await service.delete_holiday(holiday_id)

        # If error response (JSONResponse), return it directly
        if isinstance(result, JSONResponse):
            return result

        # Log successful deletion
        logger.info(
            f"Holiday deleted successfully: {holiday_id}",
            extra={
                "context": {
                    "holiday_id": holiday_id,
                    "user_id": str(getattr(current_user, 'id', 'unknown')),
                }
            },
        )

        return APIStandards.create_success_response(
            data={"deleted": True, "holiday_id": holiday_id},
            message="Holiday deleted successfully",
            title="Holiday Deleted",
        )

    except Exception as e:
        logger.error(
            f"Unexpected error deleting holiday {holiday_id}: {str(e)}", exc_info=True
        )
        return APIStandards.create_error_response(
            error_message="An unexpected error occurred while deleting the holiday",
            error_title="Internal Server Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR,
        )
