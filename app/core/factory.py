from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.base import get_db
from app.repositories.category_repository import CategoryRepository
from app.repositories.subcategory_repository import SubcategoryRepository
from app.services.category_service import CategoryService
from app.services.subcategory_service import SubcategoryService


def get_category_repository(db: AsyncSession = Depends(get_db)) -> CategoryRepository:
    """Factory for CategoryRepository."""
    return CategoryRepository(db)

def get_subcategory_repository(db: AsyncSession = Depends(get_db)) -> SubcategoryRepository:
    """Factory for SubcategoryRepository."""
    return SubcategoryRepository(db)

def get_category_service(
    repo: CategoryRepository = Depends(get_category_repository),
) -> CategoryService:
    """Factory for CategoryService."""
    return CategoryService(repo)

def get_subcategory_service(
    repo: SubcategoryRepository = Depends(get_subcategory_repository),
) -> SubcategoryService:
    """Factory for SubcategoryService."""
    return SubcategoryService(repo) 